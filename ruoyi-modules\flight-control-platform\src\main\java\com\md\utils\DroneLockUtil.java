package com.md.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.time.Duration;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * 无人机控制锁工具类
 * 使用Redisson分布式锁确保集群环境下的线程安全
 * 支持锁的自动续期和可重入特性
 */
@Component
@Slf4j
public class DroneLockUtil {
    private static final String LOCK_PREFIX = "drone:lock:";
    private static final Integer LOCK_EXPIRE_TIME = 15 * 60; // 15分钟
    private static final long LOCK_WAIT_TIME = 1; // 等待获取锁的时间（秒）

    // 用于存储操作员与锁的映射关系，支持锁的重入和操作员验证
    private final Map<String, Long> lockOperatorMap = new ConcurrentHashMap<>();

    // 获取Redisson客户端
    private final RedissonClient redissonClient = RedisUtils.getClient();

    /**
     * 尝试获取无人机控制权
     * 使用Redisson分布式锁确保原子性和线程安全
     *
     * @param uavCode    无人机编号
     * @param operatorId 操作员ID
     * @return 是否获取成功
     */
    public boolean tryLock(String uavCode, Long operatorId) {
        String lockKey = LOCK_PREFIX + uavCode;
        RLock lock = redissonClient.getLock(lockKey);

        try {
            // 检查是否是同一操作员的重入锁
            Long currentOperator = lockOperatorMap.get(lockKey);
            if (currentOperator != null && currentOperator.equals(operatorId)) {
                // 同一操作员重入，直接获取锁并刷新过期时间
                if (lock.tryLock(LOCK_WAIT_TIME, LOCK_EXPIRE_TIME, TimeUnit.SECONDS)) {
                    log.debug("操作员[{}]重入获取无人机[{}]的控制权，刷新过期时间", operatorId, uavCode);
                    return true;
                }
            }

            // 尝试获取分布式锁
            boolean acquired = lock.tryLock(LOCK_WAIT_TIME, LOCK_EXPIRE_TIME, TimeUnit.SECONDS);

            if (acquired) {
                // 获取锁成功，记录操作员信息
                lockOperatorMap.put(lockKey, operatorId);

                // 同时在Redis中存储操作员信息，用于跨节点查询
                TenantHelper.ignore(() -> {
                    String lockValue = operatorId + ":" + System.currentTimeMillis();
                    RedisUtils.setCacheObject(lockKey + ":operator", lockValue, Duration.ofSeconds(LOCK_EXPIRE_TIME));
                    return null;
                });

                log.info("操作员[{}]成功获取无人机[{}]的控制权", operatorId, uavCode);
                return true;
            } else {
                log.warn("操作员[{}]获取无人机[{}]控制权失败，可能已被其他操作员占用", operatorId, uavCode);
                return false;
            }

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("获取无人机[{}]控制锁被中断: operatorId={}", uavCode, operatorId, e);
            return false;
        } catch (Exception e) {
            log.error("获取无人机[{}]控制锁失败: operatorId={}", uavCode, operatorId, e);
            return false;
        }
    }

    /**
     * 释放无人机控制权
     * 使用Redisson分布式锁确保安全释放
     *
     * @param uavCode 无人机编号
     */
    public void unlock(String uavCode) {
        String lockKey = LOCK_PREFIX + uavCode;
        RLock lock = redissonClient.getLock(lockKey);

        try {
            // 只有持有锁的线程才能释放锁
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();

                // 清理操作员映射信息
                lockOperatorMap.remove(lockKey);

                // 清理Redis中的操作员信息
                TenantHelper.ignore(() -> {
                    RedisUtils.deleteObject(lockKey + ":operator");
                    return null;
                });

                log.info("无人机[{}]的控制权已释放", uavCode);
            } else {
                log.warn("尝试释放无人机[{}]的控制权失败，当前线程未持有该锁", uavCode);
            }
        } catch (Exception e) {
            log.error("释放无人机[{}]控制权时发生异常", uavCode, e);
        }
    }

    /**
     * 检查无人机是否被锁定
     * 使用Redisson分布式锁状态检查
     *
     * @param uavCode 无人机编号
     * @return 是否被锁定
     */
    public boolean isLocked(String uavCode) {
        String lockKey = LOCK_PREFIX + uavCode;
        RLock lock = redissonClient.getLock(lockKey);
        return lock.isLocked();
    }

    /**
     * 获取当前锁定操作员ID
     * 从Redis中查询操作员信息，支持跨节点查询
     *
     * @param uavCode 无人机编号
     * @return 操作员ID，如果未锁定则返回null
     */
    public Long getLockedOperatorId(String uavCode) {
        String lockKey = LOCK_PREFIX + uavCode;

        // 首先检查锁是否存在
        RLock lock = redissonClient.getLock(lockKey);
        if (!lock.isLocked()) {
            return null;
        }

        // 从Redis中获取操作员信息
        try {
            String value = TenantHelper.ignore(() -> RedisUtils.getCacheObject(lockKey + ":operator"));
            if (value != null && value.contains(":")) {
                return Long.parseLong(value.split(":")[0]);
            }
        } catch (Exception e) {
            log.warn("获取无人机[{}]锁定操作员ID失败", uavCode, e);
        }

        return null;
    }

    /**
     * 强制释放无人机控制权（管理员操作）
     * 用于异常情况下的锁清理
     *
     * @param uavCode 无人机编号
     * @return 是否释放成功
     */
    public boolean forceUnlock(String uavCode) {
        String lockKey = LOCK_PREFIX + uavCode;
        RLock lock = redissonClient.getLock(lockKey);

        try {
            // 强制删除锁
            lock.forceUnlock();

            // 清理相关数据
            lockOperatorMap.remove(lockKey);
            TenantHelper.ignore(() -> {
                RedisUtils.deleteObject(lockKey + ":operator");
                return null;
            });

            log.warn("管理员强制释放无人机[{}]的控制权", uavCode);
            return true;
        } catch (Exception e) {
            log.error("强制释放无人机[{}]控制权失败", uavCode, e);
            return false;
        }
    }

    /**
     * 检查当前线程是否持有指定无人机的锁
     *
     * @param uavCode 无人机编号
     * @return 是否持有锁
     */
    public boolean isHeldByCurrentThread(String uavCode) {
        String lockKey = LOCK_PREFIX + uavCode;
        RLock lock = redissonClient.getLock(lockKey);
        return lock.isHeldByCurrentThread();
    }

    /**
     * 获取锁的剩余过期时间
     *
     * @param uavCode 无人机编号
     * @return 剩余时间（毫秒），-1表示永不过期，-2表示锁不存在
     */
    public long getRemainingTimeToLive(String uavCode) {
        String lockKey = LOCK_PREFIX + uavCode;
        RLock lock = redissonClient.getLock(lockKey);
        return lock.remainTimeToLive();
    }
}