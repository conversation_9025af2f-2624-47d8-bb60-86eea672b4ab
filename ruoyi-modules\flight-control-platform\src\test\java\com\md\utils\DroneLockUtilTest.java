package com.md.utils;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * DroneLockUtil 分布式锁测试
 * 验证修复后的Redisson分布式锁功能
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class DroneLockUtilTest {

    @Resource
    private DroneLockUtil droneLockUtil;

    private static final String TEST_UAV_CODE = "TEST_DRONE_001";
    private static final Long OPERATOR_1 = 1001L;
    private static final Long OPERATOR_2 = 1002L;

    @BeforeEach
    void setUp() {
        // 清理测试数据
        droneLockUtil.forceUnlock(TEST_UAV_CODE);
    }

    /**
     * 测试基本的锁获取和释放功能
     */
    @Test
    void testBasicLockAndUnlock() {
        // 测试获取锁
        boolean locked = droneLockUtil.tryLock(TEST_UAV_CODE, OPERATOR_1);
        assertTrue(locked, "应该能够成功获取锁");

        // 验证锁状态
        assertTrue(droneLockUtil.isLocked(TEST_UAV_CODE), "无人机应该处于锁定状态");
        assertTrue(droneLockUtil.isHeldByCurrentThread(TEST_UAV_CODE), "当前线程应该持有锁");
        assertEquals(OPERATOR_1, droneLockUtil.getLockedOperatorId(TEST_UAV_CODE), "操作员ID应该匹配");

        // 测试释放锁
        droneLockUtil.unlock(TEST_UAV_CODE);
        assertFalse(droneLockUtil.isLocked(TEST_UAV_CODE), "无人机应该处于未锁定状态");
        assertNull(droneLockUtil.getLockedOperatorId(TEST_UAV_CODE), "应该没有操作员持有锁");
    }

    /**
     * 测试锁的互斥性
     */
    @Test
    void testLockMutualExclusion() {
        // 操作员1获取锁
        boolean locked1 = droneLockUtil.tryLock(TEST_UAV_CODE, OPERATOR_1);
        assertTrue(locked1, "操作员1应该能够获取锁");

        // 操作员2尝试获取同一个锁，应该失败
        boolean locked2 = droneLockUtil.tryLock(TEST_UAV_CODE, OPERATOR_2);
        assertFalse(locked2, "操作员2不应该能够获取已被占用的锁");

        // 验证锁仍然被操作员1持有
        assertEquals(OPERATOR_1, droneLockUtil.getLockedOperatorId(TEST_UAV_CODE), "锁应该仍被操作员1持有");

        // 操作员1释放锁
        droneLockUtil.unlock(TEST_UAV_CODE);

        // 操作员2现在应该能够获取锁
        boolean locked2Again = droneLockUtil.tryLock(TEST_UAV_CODE, OPERATOR_2);
        assertTrue(locked2Again, "操作员2现在应该能够获取锁");

        // 清理
        droneLockUtil.unlock(TEST_UAV_CODE);
    }

    /**
     * 测试锁的可重入性
     */
    @Test
    void testLockReentrant() {
        // 第一次获取锁
        boolean locked1 = droneLockUtil.tryLock(TEST_UAV_CODE, OPERATOR_1);
        assertTrue(locked1, "第一次获取锁应该成功");

        // 同一操作员再次获取锁（重入）
        boolean locked2 = droneLockUtil.tryLock(TEST_UAV_CODE, OPERATOR_1);
        assertTrue(locked2, "同一操作员重入获取锁应该成功");

        // 验证锁状态
        assertTrue(droneLockUtil.isLocked(TEST_UAV_CODE), "无人机应该处于锁定状态");
        assertEquals(OPERATOR_1, droneLockUtil.getLockedOperatorId(TEST_UAV_CODE), "操作员ID应该匹配");

        // 释放锁（需要释放两次，因为重入了两次）
        droneLockUtil.unlock(TEST_UAV_CODE);
        assertTrue(droneLockUtil.isLocked(TEST_UAV_CODE), "第一次释放后锁应该仍然存在");

        droneLockUtil.unlock(TEST_UAV_CODE);
        assertFalse(droneLockUtil.isLocked(TEST_UAV_CODE), "第二次释放后锁应该被完全释放");
    }

    /**
     * 测试并发场景下的锁竞争
     */
    @Test
    void testConcurrentLockCompetition() throws InterruptedException {
        final int threadCount = 10;
        final AtomicInteger successCount = new AtomicInteger(0);
        final CountDownLatch latch = new CountDownLatch(threadCount);
        final ExecutorService executor = Executors.newFixedThreadPool(threadCount);

        // 启动多个线程同时竞争锁
        for (int i = 0; i < threadCount; i++) {
            final long operatorId = 2000L + i;
            executor.submit(() -> {
                try {
                    boolean locked = droneLockUtil.tryLock(TEST_UAV_CODE, operatorId);
                    if (locked) {
                        successCount.incrementAndGet();
                        log.info("操作员[{}]成功获取锁", operatorId);
                        
                        // 模拟业务操作
                        Thread.sleep(100);
                        
                        droneLockUtil.unlock(TEST_UAV_CODE);
                        log.info("操作员[{}]释放锁", operatorId);
                    } else {
                        log.info("操作员[{}]获取锁失败", operatorId);
                    }
                } catch (Exception e) {
                    log.error("操作员[{}]执行过程中发生异常", operatorId, e);
                } finally {
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        latch.await();
        executor.shutdown();

        // 验证只有一个线程能够成功获取锁
        assertEquals(1, successCount.get(), "在并发竞争中，只应该有一个线程能够成功获取锁");
        assertFalse(droneLockUtil.isLocked(TEST_UAV_CODE), "所有操作完成后，锁应该被释放");
    }

    /**
     * 测试强制释放锁功能
     */
    @Test
    void testForceUnlock() {
        // 获取锁
        boolean locked = droneLockUtil.tryLock(TEST_UAV_CODE, OPERATOR_1);
        assertTrue(locked, "应该能够成功获取锁");

        // 验证锁状态
        assertTrue(droneLockUtil.isLocked(TEST_UAV_CODE), "无人机应该处于锁定状态");

        // 强制释放锁
        boolean forceUnlocked = droneLockUtil.forceUnlock(TEST_UAV_CODE);
        assertTrue(forceUnlocked, "强制释放锁应该成功");

        // 验证锁已被释放
        assertFalse(droneLockUtil.isLocked(TEST_UAV_CODE), "强制释放后，无人机应该处于未锁定状态");
        assertNull(droneLockUtil.getLockedOperatorId(TEST_UAV_CODE), "强制释放后，应该没有操作员持有锁");
    }

    /**
     * 测试锁的过期时间
     */
    @Test
    void testLockExpiration() {
        // 获取锁
        boolean locked = droneLockUtil.tryLock(TEST_UAV_CODE, OPERATOR_1);
        assertTrue(locked, "应该能够成功获取锁");

        // 检查剩余过期时间
        long remainingTime = droneLockUtil.getRemainingTimeToLive(TEST_UAV_CODE);
        assertTrue(remainingTime > 0, "锁应该有剩余过期时间");
        assertTrue(remainingTime <= 15 * 60 * 1000, "剩余时间不应该超过15分钟");

        log.info("锁的剩余过期时间: {}ms", remainingTime);

        // 清理
        droneLockUtil.unlock(TEST_UAV_CODE);
    }
}
